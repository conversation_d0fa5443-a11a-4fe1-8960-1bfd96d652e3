PODS:
  - boost (1.83.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.73.9)
  - FBReactNativeSpec (0.73.9):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.9)
    - RCTTypeSafety (= 0.73.9)
    - React-Core (= 0.73.9)
    - React-jsi (= 0.73.9)
    - ReactCommon/turbomodule/core (= 0.73.9)
  - fmt (6.2.1)
  - glog (0.3.5)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.73.9)
  - RCTTypeSafety (0.73.9):
    - FBLazyVector (= 0.73.9)
    - RCTRequired (= 0.73.9)
    - React-Core (= 0.73.9)
  - React (0.73.9):
    - React-Core (= 0.73.9)
    - React-Core/DevSupport (= 0.73.9)
    - React-Core/RCTWebSocket (= 0.73.9)
    - React-RCTActionSheet (= 0.73.9)
    - React-RCTAnimation (= 0.73.9)
    - React-RCTBlob (= 0.73.9)
    - React-RCTImage (= 0.73.9)
    - React-RCTLinking (= 0.73.9)
    - React-RCTNetwork (= 0.73.9)
    - React-RCTSettings (= 0.73.9)
    - React-RCTText (= 0.73.9)
    - React-RCTVibration (= 0.73.9)
  - React-callinvoker (0.73.9)
  - React-Codegen (0.73.9):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.9)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.9)
    - React-Core/RCTWebSocket (= 0.73.9)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.9)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.9)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.9):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.9)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.9)
    - React-jsi (= 0.73.9)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.9)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.9):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.9)
    - React-debug (= 0.73.9)
    - React-jsi (= 0.73.9)
    - React-jsinspector (= 0.73.9)
    - React-logger (= 0.73.9)
    - React-perflogger (= 0.73.9)
    - React-runtimeexecutor (= 0.73.9)
  - React-debug (0.73.9)
  - React-Fabric (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.9)
    - React-Fabric/attributedstring (= 0.73.9)
    - React-Fabric/componentregistry (= 0.73.9)
    - React-Fabric/componentregistrynative (= 0.73.9)
    - React-Fabric/components (= 0.73.9)
    - React-Fabric/core (= 0.73.9)
    - React-Fabric/imagemanager (= 0.73.9)
    - React-Fabric/leakchecker (= 0.73.9)
    - React-Fabric/mounting (= 0.73.9)
    - React-Fabric/scheduler (= 0.73.9)
    - React-Fabric/telemetry (= 0.73.9)
    - React-Fabric/templateprocessor (= 0.73.9)
    - React-Fabric/textlayoutmanager (= 0.73.9)
    - React-Fabric/uimanager (= 0.73.9)
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.9)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.9)
    - React-Fabric/components/modal (= 0.73.9)
    - React-Fabric/components/rncore (= 0.73.9)
    - React-Fabric/components/root (= 0.73.9)
    - React-Fabric/components/safeareaview (= 0.73.9)
    - React-Fabric/components/scrollview (= 0.73.9)
    - React-Fabric/components/text (= 0.73.9)
    - React-Fabric/components/textinput (= 0.73.9)
    - React-Fabric/components/unimplementedview (= 0.73.9)
    - React-Fabric/components/view (= 0.73.9)
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.9)
    - RCTTypeSafety (= 0.73.9)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-jsiexecutor (= 0.73.9)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.9):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.9)
    - React-utils
  - React-ImageManager (0.73.9):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jsc (0.73.9):
    - React-jsc/Fabric (= 0.73.9)
    - React-jsi (= 0.73.9)
  - React-jsc/Fabric (0.73.9):
    - React-jsi (= 0.73.9)
  - React-jserrorhandler (0.73.9):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.9):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.9)
    - React-jsi (= 0.73.9)
    - React-perflogger (= 0.73.9)
  - React-jsinspector (0.73.9)
  - React-logger (0.73.9):
    - glog
  - React-Mapbuffer (0.73.9):
    - glog
    - React-debug
  - react-native-safe-area-context (4.14.1):
    - React-Core
  - react-native-viewpager (5.0.11):
    - React-Core
  - React-nativeconfig (0.73.9)
  - React-NativeModulesApple (0.73.9):
    - glog
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.9)
  - React-RCTActionSheet (0.73.9):
    - React-Core/RCTActionSheetHeaders (= 0.73.9)
  - React-RCTAnimation (0.73.9):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.9):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-jsc
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.9):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.9):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.9):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.9):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.9)
    - React-jsi (= 0.73.9)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.9)
  - React-RCTNetwork (0.73.9):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.9):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.9):
    - React-Core/RCTTextHeaders (= 0.73.9)
    - Yoga
  - React-RCTVibration (0.73.9):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.9)
  - React-runtimeexecutor (0.73.9):
    - React-jsi (= 0.73.9)
  - React-runtimescheduler (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsc
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.9):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.9):
    - React-logger (= 0.73.9)
    - ReactCommon/turbomodule (= 0.73.9)
  - ReactCommon/turbomodule (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.9)
    - React-cxxreact (= 0.73.9)
    - React-jsi (= 0.73.9)
    - React-logger (= 0.73.9)
    - React-perflogger (= 0.73.9)
    - ReactCommon/turbomodule/bridging (= 0.73.9)
    - ReactCommon/turbomodule/core (= 0.73.9)
  - ReactCommon/turbomodule/bridging (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.9)
    - React-cxxreact (= 0.73.9)
    - React-jsi (= 0.73.9)
    - React-logger (= 0.73.9)
    - React-perflogger (= 0.73.9)
  - ReactCommon/turbomodule/core (0.73.9):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.9)
    - React-cxxreact (= 0.73.9)
    - React-jsi (= 0.73.9)
    - React-logger (= 0.73.9)
    - React-perflogger (= 0.73.9)
  - RNCMaskedView (0.1.11):
    - React
  - RNGestureHandler (2.26.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNReanimated (3.15.4):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.15.4)
    - RNReanimated/worklets (= 3.15.4)
  - RNReanimated/reanimated (3.15.4):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNReanimated/worklets (3.15.4):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.29.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-viewpager (from `../node_modules/@react-native-community/viewpager`)"
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - fmt
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-viewpager:
    :path: "../node_modules/@react-native-community/viewpager"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  FBLazyVector: 98c189b92292d4bfeac13ffa8df3ce3d84e2fc5b
  FBReactNativeSpec: 4fe1d8c2fadc7949344b197d933f76b40401aac5
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  RCT-Folly: 7169b2b1c44399c76a47b5deaaba715eeeb476c0
  RCTRequired: d362a61864a64315aee00faea8dee6cf5b3f4aad
  RCTTypeSafety: 09baf60faeab02492dc8bf04ce5af1dda645b86d
  React: b87c7c7c12f8232bd7cfdc4a00bf687144c17e30
  React-callinvoker: 67de0bc05ecb7e690345a53a1661cea9b24670b0
  React-Codegen: b14b717d942371435fe7da19e7ce59a8fcdb0274
  React-Core: 3cd38e1f80fc26f36de3d12de94f09606a26d306
  React-CoreModules: 29ad1cbe757a70575913457bb7c646b7f4d4edf0
  React-cxxreact: c14bb26596636f0b10459621081c8765fa446488
  React-debug: 937e24adc0479b9bbed3f1b7e0db68688d93c31c
  React-Fabric: dfebf54c5f4c7bba4a0990eab622687dd5f7f9e4
  React-FabricImage: 4e53f6977c4600a9d59f81a10a1ef81668b8c269
  React-graphics: 3ba4dba54c4d1426118089f1943708ef0bba8a84
  React-ImageManager: 13b4aebbffd9addbcd79d1687355db2f6d8a37e2
  React-jsc: b1cfc06106c95ce0c56994aa532c97dc09e01eb3
  React-jserrorhandler: f30af3ec30fdc04a4b080c5b1f3defb801c0c861
  React-jsi: 42c6755ff78890f68306ea47701e05eab66a222c
  React-jsiexecutor: a9f45c3e3117c78e89fd2ca13189acbd907c50f1
  React-jsinspector: aee04d04ef553d5e30e52a4de2af958cb060069f
  React-logger: 87a4232dd55485435edfa6803ff0de0b5c9eea1a
  React-Mapbuffer: 6c229dc8f1640457d1f665f0b7d79ab8f604dc8b
  react-native-safe-area-context: 141eca0fd4e4191288dfc8b96a7c7e1c2983447a
  react-native-viewpager: b99b53127d830885917ef84809c5065edd614a78
  React-nativeconfig: c1729ab95240ec80d47a2bb8354d8f31138e35a7
  React-NativeModulesApple: 8e0470cad2e6d4ae42f9ef7ad03a7a2dd1ce04d6
  React-perflogger: c93b6a895eca3f9196656bb20ce0e15ad597a4e9
  React-RCTActionSheet: 258842f426709dccbc2af31ca42b0a1807d76ad7
  React-RCTAnimation: 78c40269e35864f541b7486d17bd82a353c99fbc
  React-RCTAppDelegate: 9d22eab7bfe9ff71315bc0ef55d1068f9e0c579e
  React-RCTBlob: f948de1da266e6f63c28f8a7830a8867bb5d73a3
  React-RCTFabric: 7032719600b9a22e8acb7298bc456f276d7e2c67
  React-RCTImage: a0cdbb81db012ebc42c7dbaabdcb15f488c7c391
  React-RCTLinking: 82b6b0a5b2d5c8d3a28997e70bda46bac4be4c6e
  React-RCTNetwork: 45e30079bcb987724028c9a93c0110b6d82e4a1f
  React-RCTSettings: e67cbe694fe45b080b96b1394d4e45dff1b3ae04
  React-RCTText: 14a54686a1fa0b51b76660c7700980fdec6c3093
  React-RCTVibration: 00561f3d12dca44ed55af9060752bf8cf3fb0bfc
  React-rendererdebug: 975f3e6e430ba1a9b92dc44bc99757cb1c6cae60
  React-rncore: e7dc772ade687746b8a8a38985b4512b8d232637
  React-runtimeexecutor: bf98e8973ed4c45139fbbaf2c34af44053acc9a9
  React-runtimescheduler: 73c3f4a33418041fb7324ec1734aa27c1662bd0d
  React-utils: dab84549e65d6d711937b9c34d9f6d8fb8bd711c
  ReactCommon: 05f1f02b4b3a211d354b5f1b9b4be269abe0e386
  RNCMaskedView: 0e1bc4bfa8365eba5fbbb71e07fbdc0555249489
  RNGestureHandler: 158386e29f0d868733754a06581a4f9c2107da1a
  RNReanimated: 901eb544cac22f7a09157d0d52331849cbba3546
  RNScreens: 17e2f657f1b09a71ec3c821368a04acbb7ebcb46
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: 6aaa3c45f0fda0c6929f855121622669dabe1aaf

PODFILE CHECKSUM: 063a0ca890af22bdc7a9512dc0e5f4a084fe60d5

COCOAPODS: 1.16.2
