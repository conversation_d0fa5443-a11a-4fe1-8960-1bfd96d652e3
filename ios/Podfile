require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, '13.4'

target 'ReactNativeBridgeIos' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => false
  )

  pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler'

  target 'ReactNativeBridgeIosTests' do
    inherit! :complete
    # Pods for testing
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable the next line.
  # Disabled Flipper due to fmt library C++ compatibility issues with Xcode 18.4
  # use_flipper!()

  post_install do |installer|
    react_native_post_install(installer)

    # Fix C++ compatibility issues with Xcode 18.4
    installer.pods_project.targets.each do |target|
      if target.name == 'fmt'
        target.build_configurations.each do |config|
          config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'c++17'
          config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)']
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'FMT_UNICODE=0'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'FMT_USE_CHAR8_T=0'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << '_LIBCPP_DISABLE_AVAILABILITY'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'FMT_DISABLE_CHAR8_T'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'FMT_HEADER_ONLY=1'
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] << '-DFMT_USE_CHAR8_T=0'
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] << '-DFMT_DISABLE_CHAR8_T'
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] << '-DFMT_HEADER_ONLY=1'
          # Exclude the problematic format.cc file from compilation
          config.build_settings['EXCLUDED_SOURCE_FILE_NAMES'] ||= []
          config.build_settings['EXCLUDED_SOURCE_FILE_NAMES'] << 'format.cc'
        end
      end

      # Fix char_traits<unsigned char> compatibility issues for multiple React Native components
      if ['React-rendererdebug', 'React-jsiexecutor', 'ReactCommon', 'React-cxxreact', 'React-utils', 'React-jsi', 'React-Fabric', 'React-FabricImage', 'React-ImageManager', 'React-graphics', 'React-CoreModules', 'React-RCTFabric', 'React-callinvoker', 'React-runtimescheduler'].include?(target.name)
        target.build_configurations.each do |config|
          config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'c++17'
          config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)']
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << '_LIBCPP_DISABLE_AVAILABILITY'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'FMT_USE_CHAR8_T=0'
          config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'FMT_DISABLE_CHAR8_T'
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] ||= ['$(inherited)']
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] << '-DFMT_USE_CHAR8_T=0'
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] << '-DFMT_DISABLE_CHAR8_T'
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] << '-Wno-error=implicit-instantiation-undefined-template'
          config.build_settings['OTHER_CPLUSPLUSFLAGS'] << '-Wno-implicit-instantiation-undefined-template'

          # Exclude problematic files for specific targets
          if target.name == 'React-rendererdebug'
            config.build_settings['EXCLUDED_SOURCE_FILE_NAMES'] ||= []
            config.build_settings['EXCLUDED_SOURCE_FILE_NAMES'] << 'DebugStringConvertible.cpp'
          end
        end
      end
    end

    installer.generated_projects.each do |project|
      project.targets.each do |target|
          target.build_configurations.each do |config|
              config.build_settings["DEVELOPMENT_TEAM"] = "WF47643S5X"
              config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION']
           end
      end
    end
  end
end
